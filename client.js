"use strict";
/* ``````````` ## Development By el8rbawY ## ```````````*/
const config = {
    weapons: {
        name: 'متجر الأسلحة النارية',
        title: 'ﺔﺤﻠﺳﻻﺍ ﺮﺠﺘﻣ',
        removeOwner: true,
        blip: 110,
        coords: [
            { x: -329.8931, y: 6081.6006, z: 31.4589, h: 136.4776 },
            { x: 1694.1322, y: 3757.8313, z: 34.7245, h: 137.6979 },
            { x: -3169.0681, y: 1085.6702, z: 20.8381, h: 340.1574 },
            { x: 2569.1279, y: 296.0753, z: 108.7350, h: 267.7120 },
            { x: 811.0543, y: -2154.7068, z: 29.6192, h: 176.6844 },
            { x: 250.9146, y: -48.2514, z: 69.9450, h: 337.2215 },
            { x: 843.5459, y: -1031.7297, z: 28.2003, h: 270.8934 },
            { x: -663.3336, y: -937.2455, z: 21.8486, h: 87.9139 },
        ],
        items: [
            { id: 'weapon_petrolcan', title: 'جالون وقود', price: 3500 },
            { id: 'clip', title: 'تعبئة الطلقات', price: 14999 },
            { id: 'bulletproof100', title: 'درع 100%', price: 20000 },
            { id: 'weapon_combatpistol', title: 'مسدس مصغر', price: 25000, level: 20 },
            { id: 'weapon_flashlight', title: 'مصباح يدوي', price: 5000 },
            { id: 'weapon_heavypistol', title: 'مسدس قوي', price: 100000, level: 65 },
            { id: 'weapon_bat', title: 'مطرقة', price: 15000, level: 10 },
            { id: 'gadget_parachute', title: 'مظلة قفز', price: 15000, level: 0 },
        ]
    },
    blackMarket: {
        name: 'السوق السوداء (غير شرعي)',
        title: 'ﺀﺍﺩﻮﺴﻟﺍ ﻕﻮﺴﻟﺍ',
        removeOwner: true,
        blip: 310,
        coords: [
            { x: -1117.4714, y: 2696.3364, z: 18.5612, h: 128.9488, bot: [-1112.8758, 2698.7954, -17.5647, 130], isCreateBot: true },
        ],
        items: [
            { id: 'weapon_petrolcan', title: 'جالون وقود', price: 3500, level: 0 },
            { id: 'gadget_parachute', title: 'مظلة قفز', price: 5000, level: 0 },
            { id: 'weapon_bottle', title: 'بطل', price: 10000, level: 20 },
            { id: 'weapon_knuckle', title: 'مضرب حديدي', price: 15000, level: 20 },
            { id: 'weapon_molotov', title: 'مولوتوف', price: 20000, level: 20 },
            { id: 'weapon_knife', title: 'سكين', price: 27000, level: 25 },
            { id: 'weapon_switchblade', title: 'شفرة', price: 30000, level: 30 },
            { id: 'removeclhand', title: 'فك القيد', price: 35000, level: 30 },
            { id: 'weapon_pumpshotgun', title: 'شوزن', price: 130000, level: 40 },
            { id: 'weapon_sawnoffshotgun', title: 'شوزن مصغر', price: 190000, level: 55 },
            { id: 'weapon_microsmg', title: 'رشاش مايكرو', price: 230000, level: 55 },
            { id: 'weapon_minismg', title: 'رشاش SMG مصغر', price: 300000, level: 65 },
            { id: 'weapon_smg', title: 'رشاش ألي', price: 340000, level: 65 },
            { id: 'weapon_assaultrifle', title: 'رشاش كلاشنكوف', price: 750000, level: 150 },
            { id: 'waepon_doubleaction', title: 'مسدس كاوبوي', price: 1200000, level: 180 },
            { id: 'weapon_bullpuprifle', title: 'رشاش قتال مطور', price: 1500000, level: 200 },
        ]
    }
};
/* ``````````` ## Development By el8rbawY ## ```````````*/
const state = {
    coords: [],
    isOpen: false,
    runClose: false,
    currentShop: ''
};
const empty = null;
/* ``````````` ## Development By el8rbawY ## ```````````*/
for (let key in config) { // @ts-ignore
    const ref = config[key];
    for (let coords of ref.coords) {
        const blip = AddBlipForCoord(coords.x, coords.y, coords.z);
        SetBlipSprite(blip, ref.blip);
        SetBlipAsShortRange(blip, true);
        BeginTextCommandSetBlipName("STRING");
        AddTextComponentString(`<font face="A9eelsh">${ref.title}</font>`);
        EndTextCommandSetBlipName(blip);
        if (key === 'blackMarket')
            SetBlipColour(blip, 6);
    }
}
// ---
setTick(() => {
    const pedID = PlayerPedId();
    state.coords = GetEntityCoords(pedID, true);
    let currentMarker = null;
    for (let key in config) {
        const color = key === 'blackMarket' ? [146, 46, 39, 255] : [22, 24, 29, 255];
        // @ts-ignore
        if (!config[key].coords)
            continue;
        // @ts-ignore
        for (let coords of config[key].coords) {
            const distance = GetDistanceBetweenCoords(state.coords[0], state.coords[1], state.coords[2], coords.x, coords.y, coords.z, true);
            if (distance < 20) { // @ts-ignore
                DrawMarker(1, coords.x, coords.y, coords.z - 1, 0.0, 0.0, 0.0, 0.0, 0, 0.0, 1.0, 1.0, 0.25, ...color, false, false, 2, false, empty, empty, false);
                currentMarker = { id: coords.id, key, distance };
                break;
            }
        }
        if (currentMarker)
            break;
    }
    if (currentMarker && currentMarker.distance < 0.6 && !IsEntityDead(pedID) && !IsPauseMenuActive()) {
        if (IsControlJustPressed(0, 38)) {
            openUI(currentMarker.key);
        }
        else if (!state.isOpen && !state.runClose) {
            SendNUIMessage({ type: 'entranceOpen', key: currentMarker.key });
        }
        state.runClose = true;
    }
    else if (state.runClose) {
        closeUI(true);
    }
});
// ---
setTimeout(() => {
    const createBotsID = setTick(async () => {
        if (config.blackMarket.coords.every(i => i.isCreateBot))
            clearTick(createBotsID);
        // create
        for (let item of config.blackMarket.coords) {
            const distance = GetDistanceBetweenCoords(state.coords[0], state.coords[1], state.coords[2], item.x, item.y, item.z, true);
            if (distance < 30 && !item.isCreateBot) {
                item.isCreateBot = true;
                setTimeout(() => {
                    const pedID = CreatePed(1, -1275859404, item.bot[0], item.bot[1], item.bot[2], item.bot[3], false, false);
                    GiveWeaponToPed(pedID, -1075685676, 1000, false, true);
                    SetCurrentPedWeapon(pedID, -1075685676, true);
                    FreezeEntityPosition(pedID, true);
                    SetBlockingOfNonTemporaryEvents(pedID, true);
                    SetEntityInvincible(pedID, true);
                    TaskPlayAnim(pedID, 'amb@world_human_stand_guard@male@base', 'base', 8.0, 1.0, -1, 1, 1.0, false, false, false);
                }, 5000);
            }
        }
    });
}, 5000);
// ---
RegisterNuiCallbackType('NUI:payment');
on('__cfx_nui:NUI:payment', (data, cb) => {
    if (state.currentShop === 'external') {
        for (const item of data) {
            exports.esx_shops2.buyItem(JSON.stringify({ Count: item.count, Item: item.id }));
        }
        closeUI(true);
        cb('success');
    }
    else {
        // @ts-ignore
        const price = data.reduce((total, obj) => total + (config[state.currentShop].items.find((item) => item.id === obj.id).price * obj.count), 0);
        const accounts = exports.es_extended.getSharedObject().PlayerData.accounts;
        let isMoneyRed = false;
        if (state.currentShop === 'blackMarket') {
            const moneyRed = accounts.find((item) => item.name === 'black_money').money;
            if (moneyRed < price || !moneyRed) {
                exports.OscarCounty_Notifications.showAttention('error', 'للأسف لا تملك الأموال الغير شرعية الكافية للشراء!');
                closeUI(true);
                return cb('OK!');
            }
            else
                isMoneyRed = true;
        }
        else {
            const cash = accounts.find((item) => item.name === 'money').money;
            const bank = accounts.find((item) => item.name === 'bank').money;
            if (cash < price && bank < price) { // Failed
                exports.OscarCounty_Notifications.showAttention('error', 'للأسف لا تملك المال الكافي!');
                closeUI(true);
                return cb('OK!');
            }
        }
        emitNet('HyperScript_Stores:handleGeneral-server', 'payment', { items: data, price, isMoneyRed });
        exports.OscarCounty_Notifications.showAttention('success', 'لقد قمت للتو بدفع بعض المال.');
        closeUI(true);
        cb('success');
    }
});
// ---
RegisterNuiCallbackType('NUI:closeUI');
on('__cfx_nui:NUI:closeUI', (_, cb) => { closeUI(false); cb('OK!'); });
/* ``````````` ## Development By el8rbawY ## ```````````*/
const Delay = (ms) => new Promise(res => setTimeout(res, ms));
function openUI(key, data) {
    SendNUIMessage({ type: 'open', storeKey: key, info: data || getInfo(key) });
    SetNuiFocus(true, true);
    state.isOpen = true;
    state.currentShop = key;
}
// ---
function getInfo(key) {
    const data = config[key].items.map(i => ({
        ...i, count: 0, level: i.level ? exports.ESX_SystemXpLevel.method('getLevel') < i.level ? i.level : null : null
    }));
    return {
        // @ts-ignore
        removeOwner: config[key].removeOwner,
        // current: exports.NewStart_Inventory.info('currentKG'),
        // max: exports.NewStart_Inventory.info('maxKG'), 
        items: data,
        // @ts-ignore
        title: config[key].name
    };
}
// ---
function closeUI(isNUI) {
    if (state.isOpen) {
        state.isOpen = false;
        SetNuiFocus(false, false);
        exports.esx_shops2.closeUI();
    }
    if (isNUI)
        SendNUIMessage({ type: 'closeUI' });
    state.runClose = false;
}
// ---
exports('method', (type, data) => {
    if (type === 'openShop') {
        data = JSON.parse(data);
        openUI('external', {
            title: data.ShopName,
            items: data.result.map((item) => ({
                id: item.item, title: item.label, count: 0, price: item.price, image: item.src,
                level: item.level ? exports.ESX_SystemXpLevel.method('getLevel') < parseInt(item.level) ? item.level : null : null
            }))
        });
    }
});
