"use strict";
/* ``````````` ## Development By el8rbawY ## ```````````*/

// ESX Framework
const ESX = global.exports.es_extended.getSharedObject();

// Server Events
RegisterNetEvent('HyperScript_Stores:handleGeneral-server');
onNet('HyperScript_Stores:handleGeneral-server', (type, data) => {
    const source = global.source;
    const xPlayer = ESX.GetPlayerFromId(source);
    
    if (!xPlayer) {
        console.log(`[OscarCounty_Stores] Player not found: ${source}`);
        return;
    }

    if (type === 'payment') {
        handlePayment(source, xPlayer, data);
    }
});

// Handle Payment Function
function handlePayment(source, xPlayer, data) {
    const { items, price, isMoneyRed } = data;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
        console.log(`[OscarCounty_Stores] Invalid items data from player: ${source}`);
        return;
    }

    if (!price || price <= 0) {
        console.log(`[OscarCounty_Stores] Invalid price from player: ${source}`);
        return;
    }

    // Check if player has enough money
    let hasEnoughMoney = false;
    
    if (isMoneyRed) {
        // Black market - use black money
        const blackMoney = xPlayer.getAccount('black_money');
        if (blackMoney && blackMoney.money >= price) {
            hasEnoughMoney = true;
            xPlayer.removeAccountMoney('black_money', price);
            console.log(`[OscarCounty_Stores] Player ${xPlayer.getName()} paid ${price} black money`);
        }
    } else {
        // Regular store - use cash or bank
        const cash = xPlayer.getAccount('money');
        const bank = xPlayer.getAccount('bank');
        
        if (cash && cash.money >= price) {
            hasEnoughMoney = true;
            xPlayer.removeAccountMoney('money', price);
            console.log(`[OscarCounty_Stores] Player ${xPlayer.getName()} paid ${price} cash`);
        } else if (bank && bank.money >= price) {
            hasEnoughMoney = true;
            xPlayer.removeAccountMoney('bank', price);
            console.log(`[OscarCounty_Stores] Player ${xPlayer.getName()} paid ${price} from bank`);
        }
    }

    if (!hasEnoughMoney) {
        console.log(`[OscarCounty_Stores] Player ${xPlayer.getName()} doesn't have enough money`);
        return;
    }

    // Give items to player
    for (const item of items) {
        if (!item.id || !item.count || item.count <= 0) {
            console.log(`[OscarCounty_Stores] Invalid item data: ${JSON.stringify(item)}`);
            continue;
        }

        // Check if it's a weapon
        if (item.id.startsWith('weapon_') || item.id.startsWith('gadget_')) {
            // Handle weapons
            if (item.id === 'gadget_parachute') {
                xPlayer.addInventoryItem('parachute', item.count);
            } else if (item.id === 'clip') {
                // Handle ammo/clips
                xPlayer.addInventoryItem('clip', item.count);
            } else if (item.id === 'bulletproof100') {
                // Handle armor
                xPlayer.addInventoryItem('bulletproof', item.count);
            } else {
                // Handle regular weapons
                xPlayer.addWeapon(item.id, 250); // 250 ammo per weapon
            }
        } else {
            // Handle regular items
            xPlayer.addInventoryItem(item.id, item.count);
        }

        console.log(`[OscarCounty_Stores] Gave ${item.count}x ${item.id} to ${xPlayer.getName()}`);
    }

    // Log the transaction
    console.log(`[OscarCounty_Stores] Transaction completed for ${xPlayer.getName()}: ${items.length} items, total: ${price}`);
}

// Utility function to check if player can carry item
function canCarryItem(xPlayer, item, count) {
    const inventoryItem = xPlayer.getInventoryItem(item);
    if (!inventoryItem) return false;
    
    return (inventoryItem.count + count) <= inventoryItem.limit;
}

// Export functions for other resources
exports('getPlayerMoney', (playerId, accountType = 'money') => {
    const xPlayer = ESX.GetPlayerFromId(playerId);
    if (!xPlayer) return 0;
    
    const account = xPlayer.getAccount(accountType);
    return account ? account.money : 0;
});

exports('removePlayerMoney', (playerId, amount, accountType = 'money') => {
    const xPlayer = ESX.GetPlayerFromId(playerId);
    if (!xPlayer) return false;
    
    const account = xPlayer.getAccount(accountType);
    if (!account || account.money < amount) return false;
    
    xPlayer.removeAccountMoney(accountType, amount);
    return true;
});

exports('givePlayerItem', (playerId, item, count) => {
    const xPlayer = ESX.GetPlayerFromId(playerId);
    if (!xPlayer) return false;
    
    if (item.startsWith('weapon_')) {
        xPlayer.addWeapon(item, 250);
    } else {
        xPlayer.addInventoryItem(item, count);
    }
    return true;
});

console.log('[OscarCounty_Stores] Server script loaded successfully!');
